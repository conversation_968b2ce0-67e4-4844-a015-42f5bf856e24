#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test3优化效果验证脚本

本脚本用于验证Test3数据优化后的效果：
1. 对比优化前后的HEP波形图
2. 检查前额叶区域双峰现象是否消除
3. 对比信号质量指标
4. 验证是否达到与Test1、Test2相似的质量水平
"""

import os
import h5py
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import font_manager
from scipy.signal import find_peaks, butter, filtfilt
from scipy.ndimage import gaussian_filter1d
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(font_path):
    font_manager.fontManager.addfont(font_path)
    plt.rcParams['font.family'] = 'LXGW Wenkai'
plt.rcParams['font.size'] = 10
plt.rcParams['axes.unicode_minus'] = False

# 定义路径
BASE_DIR = r'D:\ecgeeg\30-数据分析\5-HBA'
DATA_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '13_raw_epochs', 'tp9tp10')
OPTIMIZATION_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '14_rest_vs_test_analysis', 'tp9tp10', 'test3_optimization')
RESULT_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '14_rest_vs_test_analysis', 'tp9tp10', 'test3_validation')
os.makedirs(RESULT_DIR, exist_ok=True)

# 定义脑区电极分组
BRAIN_REGIONS = {
    '左前额叶区域': ['Fp1', 'AF3', 'AF7', 'F3', 'F5'],
    '右前额叶区域': ['Fp2', 'AF4', 'AF8', 'F4', 'F6'],
    '额中央区域': ['Fpz', 'AFz', 'Fz', 'FCz'],
    '中央区域': ['Cz', 'CPz', 'Pz']
}

# 时间窗口设置
BASELINE_WINDOW = (-0.2, 0.0)
HEP_WINDOW = (0.2, 0.6)

def load_hep_data(h5_path):
    """加载HEP数据"""
    print(f"正在加载数据: {os.path.basename(h5_path)}")

    with h5py.File(h5_path, 'r') as f:
        data = f['data'][:]
        ch_names = [ch.decode('utf-8').strip() if isinstance(ch, bytes) else str(ch).strip()
                   for ch in f['ch_names'][:]]
        times = f['times'][:]

        if 'subject_ids' in f:
            subject_ids = f['subject_ids'][:]
            subject_ids = [s.decode('utf-8') if isinstance(s, bytes) else str(s)
                          for s in subject_ids]
        else:
            subject_ids = ['unknown'] * data.shape[0]

    sampling_freq = 1 / (times[1] - times[0])
    print(f"  数据形状: {data.shape}")
    print(f"  采样频率: {sampling_freq:.1f} Hz")
    print(f"  时间范围: {times[0]*1000:.1f} 到 {times[-1]*1000:.1f} ms")

    return data, ch_names, times, subject_ids, sampling_freq

def apply_standard_preprocessing(data, sampling_freq):
    """应用标准预处理流程（与其他阶段保持一致）"""
    print("应用标准预处理流程...")

    # 1. 带通滤波 0.5-45Hz
    print("  应用带通滤波: 0.5-45Hz")
    nyquist = sampling_freq / 2
    low_norm = max(0.5 / nyquist, 0.001)
    high_norm = min(45.0 / nyquist, 0.999)
    b, a = butter(4, [low_norm, high_norm], btype='band')

    filtered_data = np.zeros_like(data)
    for i in range(data.shape[0]):
        for j in range(data.shape[1]):
            filtered_data[i, j, :] = filtfilt(b, a, data[i, j, :])

    return filtered_data

def apply_baseline_correction(data, times):
    """应用基线矫正"""
    print("应用基线矫正...")

    baseline_mask = (times >= BASELINE_WINDOW[0]) & (times <= BASELINE_WINDOW[1])
    corrected_data = data.copy()

    for epoch in range(data.shape[0]):
        for ch in range(data.shape[1]):
            baseline_mean = np.mean(data[epoch, ch, baseline_mask])
            corrected_data[epoch, ch, :] -= baseline_mean

    return corrected_data

def apply_gaussian_smoothing(data, sigma=1.5):
    """应用高斯平滑"""
    print(f"应用高斯平滑，标准差: {sigma}")

    smoothed_data = np.zeros_like(data)
    for i in range(data.shape[0]):
        for j in range(data.shape[1]):
            smoothed_data[i, j, :] = gaussian_filter1d(data[i, j, :], sigma=sigma)

    return smoothed_data

def calculate_quality_metrics(data, times):
    """计算信号质量指标"""
    baseline_mask = (times >= BASELINE_WINDOW[0]) & (times <= BASELINE_WINDOW[1])
    hep_mask = (times >= HEP_WINDOW[0]) & (times <= HEP_WINDOW[1])

    # 基本统计
    max_amplitude = np.max(np.abs(data)) * 1e6
    mean_amplitude = np.mean(np.abs(data)) * 1e6
    std_amplitude = np.std(data) * 1e6

    # 基线稳定性
    baseline_data = data[:, :, baseline_mask]
    baseline_std = np.std(baseline_data) * 1e6
    baseline_mean = np.mean(baseline_data) * 1e6

    # HEP窗口信号
    hep_data = data[:, :, hep_mask]
    hep_std = np.std(hep_data) * 1e6
    hep_mean = np.mean(hep_data) * 1e6

    # 信噪比
    snr = hep_std / baseline_std if baseline_std > 0 else 0

    return {
        'max_amplitude': max_amplitude,
        'mean_amplitude': mean_amplitude,
        'std_amplitude': std_amplitude,
        'baseline_std': baseline_std,
        'baseline_mean': baseline_mean,
        'hep_std': hep_std,
        'hep_mean': hep_mean,
        'snr': snr
    }

def detect_peaks_in_frontal_region(data, times, ch_names, sampling_freq):
    """检测前额叶区域的峰值"""
    frontal_channels = ['Fp1', 'Fp2', 'Fpz', 'AF3', 'AF4', 'AFz', 'F3', 'F4', 'Fz']
    frontal_indices = [i for i, ch in enumerate(ch_names) if ch in frontal_channels]

    if not frontal_indices:
        print("⚠️ 警告: 未找到前额叶电极")
        return {'count': 0, 'times': [], 'amplitudes': []}

    # 计算前额叶区域平均波形
    frontal_avg = np.mean(data[:, frontal_indices, :], axis=(0, 1)) * 1e6

    # 在HEP窗口内检测峰值
    hep_mask = (times >= HEP_WINDOW[0]) & (times <= HEP_WINDOW[1])
    hep_signal = frontal_avg[hep_mask]
    hep_times = times[hep_mask] * 1000  # 转换为ms

    # 使用find_peaks检测峰值
    peaks, _ = find_peaks(np.abs(hep_signal),
                         height=np.max(np.abs(hep_signal)) * 0.3,
                         distance=int(0.05 * sampling_freq))  # 最小间距50ms

    peak_times = hep_times[peaks] if len(peaks) > 0 else []
    peak_amplitudes = hep_signal[peaks] if len(peaks) > 0 else []

    return {
        'count': len(peaks),
        'times': peak_times.tolist() if len(peak_times) > 0 else [],
        'amplitudes': peak_amplitudes.tolist() if len(peak_amplitudes) > 0 else [],
        'signal': hep_signal.tolist(),
        'time_axis': hep_times.tolist()
    }

def create_validation_visualization(original_data, optimized_data, times, ch_names,
                                  original_metrics, optimized_metrics,
                                  original_peaks, optimized_peaks):
    """创建验证可视化图表"""
    print("创建验证可视化图表...")

    # 创建大图表：2行3列布局
    fig = plt.figure(figsize=(18, 12))

    # 设置总标题
    fig.suptitle('Test3数据优化效果验证\n'
                 '双侧乳突参考 (TP9/TP10) | 0.5-45Hz + 高斯平滑',
                 fontsize=16, fontweight='bold', y=0.95)

    # 创建子图布局
    gs = fig.add_gridspec(2, 3, hspace=0.35, wspace=0.3)

    # 颜色方案
    colors = {
        'original': '#8B4513',    # 深棕色 - 原始Test3
        'optimized': '#228B22'    # 深绿色 - 优化后Test3
    }

    # 1. 前额叶区域HEP波形对比 (左上)
    ax1 = fig.add_subplot(gs[0, 0])

    frontal_channels = ['Fp1', 'Fp2', 'Fpz', 'AF3', 'AF4', 'AFz', 'F3', 'F4', 'Fz']
    frontal_indices = [i for i, ch in enumerate(ch_names) if ch in frontal_channels]

    if frontal_indices:
        original_frontal = np.mean(original_data[:, frontal_indices, :], axis=(0, 1)) * 1e6
        optimized_frontal = np.mean(optimized_data[:, frontal_indices, :], axis=(0, 1)) * 1e6

        ax1.plot(times * 1000, original_frontal, color=colors['original'],
                linewidth=1.5, label='优化前Test3', alpha=0.9)
        ax1.plot(times * 1000, optimized_frontal, color=colors['optimized'],
                linewidth=1.5, label='优化后Test3', alpha=0.9)

    ax1.set_xlim(-200, 650)
    ax1.set_xlabel('时间 (ms)', fontsize=10)
    ax1.set_ylabel('幅值 (μV)', fontsize=10)
    ax1.set_title('前额叶区域HEP波形对比', fontsize=12, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    ax1.axhline(0, color='black', linewidth=0.5, alpha=0.6)
    ax1.axvline(0, color='gray', linewidth=0.5, alpha=0.5, linestyle='--')
    ax1.axvline(200, color='red', linewidth=0.5, alpha=0.7, linestyle=':')
    ax1.axvline(600, color='red', linewidth=0.5, alpha=0.7, linestyle=':')
    ax1.legend(fontsize=9)

    # 2. 信号质量指标对比 (中上)
    ax2 = fig.add_subplot(gs[0, 1])

    metrics_names = ['最大幅度', '基线标准差', '信噪比']
    original_values = [original_metrics['max_amplitude'],
                      original_metrics['baseline_std'],
                      original_metrics['snr']]
    optimized_values = [optimized_metrics['max_amplitude'],
                       optimized_metrics['baseline_std'],
                       optimized_metrics['snr']]

    x = np.arange(len(metrics_names))
    width = 0.35

    ax2.bar(x - width/2, original_values, width, label='优化前',
           color=colors['original'], alpha=0.7)
    ax2.bar(x + width/2, optimized_values, width, label='优化后',
           color=colors['optimized'], alpha=0.7)

    ax2.set_xlabel('质量指标', fontsize=10)
    ax2.set_ylabel('数值', fontsize=10)
    ax2.set_title('信号质量指标对比', fontsize=12, fontweight='bold')
    ax2.set_xticks(x)
    ax2.set_xticklabels(metrics_names)
    ax2.legend()
    ax2.set_yscale('log')  # 使用对数刻度以便更好地显示差异

    # 3. 峰值检测结果对比 (右上)
    ax3 = fig.add_subplot(gs[0, 2])

    # 显示峰值数量对比
    peak_counts = [original_peaks['count'], optimized_peaks['count']]
    peak_labels = ['优化前', '优化后']

    bars = ax3.bar(peak_labels, peak_counts, color=[colors['original'], colors['optimized']], alpha=0.7)
    ax3.set_ylabel('峰值数量', fontsize=10)
    ax3.set_title('HEP峰值检测结果', fontsize=12, fontweight='bold')
    ax3.grid(True, alpha=0.3, axis='y')

    # 在柱状图上标注数值
    for bar, count in zip(bars, peak_counts):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{count}', ha='center', va='bottom', fontweight='bold')

    # 4. 优化前峰值详细分析 (左下)
    ax4 = fig.add_subplot(gs[1, 0])

    if original_peaks['signal'] and original_peaks['time_axis']:
        original_signal = np.array(original_peaks['signal'])
        original_time_axis = np.array(original_peaks['time_axis'])

        ax4.plot(original_time_axis, original_signal, color=colors['original'],
                linewidth=2, label='优化前HEP信号')

        # 标记峰值
        if original_peaks['times'] and original_peaks['amplitudes']:
            for i, (time, amp) in enumerate(zip(original_peaks['times'], original_peaks['amplitudes'])):
                ax4.plot(time, amp, 'ro', markersize=8)
                ax4.annotate(f'{time:.0f}ms', (time, amp),
                           xytext=(5, 5), textcoords='offset points', fontsize=8)

    ax4.set_xlabel('时间 (ms)', fontsize=10)
    ax4.set_ylabel('幅值 (μV)', fontsize=10)
    ax4.set_title('优化前峰值检测', fontsize=12, fontweight='bold')
    ax4.grid(True, alpha=0.3)
    ax4.axhline(0, color='black', linewidth=0.5, alpha=0.6)

    # 5. 优化后峰值详细分析 (中下)
    ax5 = fig.add_subplot(gs[1, 1])

    if optimized_peaks['signal'] and optimized_peaks['time_axis']:
        optimized_signal = np.array(optimized_peaks['signal'])
        optimized_time_axis = np.array(optimized_peaks['time_axis'])

        ax5.plot(optimized_time_axis, optimized_signal, color=colors['optimized'],
                linewidth=2, label='优化后HEP信号')

        # 标记峰值
        if optimized_peaks['times'] and optimized_peaks['amplitudes']:
            for i, (time, amp) in enumerate(zip(optimized_peaks['times'], optimized_peaks['amplitudes'])):
                ax5.plot(time, amp, 'go', markersize=8)
                ax5.annotate(f'{time:.0f}ms', (time, amp),
                           xytext=(5, 5), textcoords='offset points', fontsize=8)

    ax5.set_xlabel('时间 (ms)', fontsize=10)
    ax5.set_ylabel('幅值 (μV)', fontsize=10)
    ax5.set_title('优化后峰值检测', fontsize=12, fontweight='bold')
    ax5.grid(True, alpha=0.3)
    ax5.axhline(0, color='black', linewidth=0.5, alpha=0.6)

    # 6. 优化效果总结 (右下)
    ax6 = fig.add_subplot(gs[1, 2])
    ax6.axis('off')

    # 生成优化效果总结文本
    summary_text = generate_optimization_summary(original_metrics, optimized_metrics,
                                                original_peaks, optimized_peaks)
    ax6.text(0.05, 0.95, summary_text, transform=ax6.transAxes, fontsize=11,
             verticalalignment='top', horizontalalignment='left',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8))

    plt.tight_layout()

    # 保存图片
    output_file = os.path.join(RESULT_DIR, 'test3_optimization_validation.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"验证图表已保存: {output_file}")

    plt.show()
    return fig

def generate_optimization_summary(original_metrics, optimized_metrics, original_peaks, optimized_peaks):
    """生成优化效果总结"""
    summary = "Test3优化效果总结:\n\n"

    # 1. 双峰现象检查
    if original_peaks['count'] >= 2 and optimized_peaks['count'] < 2:
        summary += "✅ 双峰现象已消除\n"
        summary += f"   峰值数量: {original_peaks['count']} → {optimized_peaks['count']}\n\n"
    elif original_peaks['count'] >= 2 and optimized_peaks['count'] >= 2:
        summary += "⚠️ 双峰现象仍存在\n"
        summary += f"   峰值数量: {original_peaks['count']} → {optimized_peaks['count']}\n\n"
    else:
        summary += "ℹ️ 原始数据无明显双峰\n\n"

    # 2. 信号质量改善
    amp_reduction = (original_metrics['max_amplitude'] - optimized_metrics['max_amplitude']) / original_metrics['max_amplitude'] * 100
    noise_reduction = (original_metrics['baseline_std'] - optimized_metrics['baseline_std']) / original_metrics['baseline_std'] * 100
    snr_change = (optimized_metrics['snr'] - original_metrics['snr']) / original_metrics['snr'] * 100

    summary += "信号质量改善:\n"
    summary += f"• 最大幅度降低: {amp_reduction:.1f}%\n"
    summary += f"• 基线噪声降低: {noise_reduction:.1f}%\n"
    summary += f"• 信噪比变化: {snr_change:+.1f}%\n\n"

    # 3. 数据量变化
    summary += f"数据保留情况:\n"
    summary += f"• 优化前: {original_metrics.get('epoch_count', 'N/A')} epochs\n"
    summary += f"• 优化后: {optimized_metrics.get('epoch_count', 'N/A')} epochs\n"

    return summary

def main():
    """主函数 - Test3优化效果验证"""
    print("=" * 80)
    print("Test3数据优化效果验证")
    print("=" * 80)

    try:
        # 步骤1: 加载原始Test3数据
        print("\n步骤1: 加载原始Test3数据")
        test3_files = [f for f in os.listdir(DATA_DIR) if f.startswith('test3_raw_epochs_') and f.endswith('.h5')]
        if not test3_files:
            print("❌ 未找到原始Test3数据文件")
            return

        test3_files.sort()
        original_test3_file = os.path.join(DATA_DIR, test3_files[-1])
        original_data, ch_names, times, _, sampling_freq = load_hep_data(original_test3_file)

        # 步骤2: 加载优化后Test3数据
        print("\n步骤2: 加载优化后Test3数据")
        optimized_files = [f for f in os.listdir(OPTIMIZATION_DIR) if f.startswith('test3_optimized_epochs_') and f.endswith('.h5')]
        if not optimized_files:
            print("❌ 未找到优化后Test3数据文件")
            return

        optimized_files.sort()
        optimized_test3_file = os.path.join(OPTIMIZATION_DIR, optimized_files[-1])
        optimized_data, _, _, _, _ = load_hep_data(optimized_test3_file)

        # 步骤3: 对原始数据应用标准预处理（与优化后数据保持一致）
        print("\n步骤3: 对原始数据应用标准预处理")
        original_processed = apply_standard_preprocessing(original_data, sampling_freq)
        original_processed = apply_baseline_correction(original_processed, times)
        original_processed = apply_gaussian_smoothing(original_processed, 1.5)

        # 步骤4: 对优化后数据应用相同的预处理
        print("\n步骤4: 对优化后数据应用标准预处理")
        optimized_processed = apply_standard_preprocessing(optimized_data, sampling_freq)
        optimized_processed = apply_baseline_correction(optimized_processed, times)
        optimized_processed = apply_gaussian_smoothing(optimized_processed, 1.5)

        # 步骤5: 计算质量指标
        print("\n步骤5: 计算质量指标")
        original_metrics = calculate_quality_metrics(original_processed, times)
        optimized_metrics = calculate_quality_metrics(optimized_processed, times)

        # 添加epoch数量信息
        original_metrics['epoch_count'] = original_processed.shape[0]
        optimized_metrics['epoch_count'] = optimized_processed.shape[0]

        print("原始Test3质量指标:")
        print(f"  Epochs数量: {original_metrics['epoch_count']}")
        print(f"  最大幅度: {original_metrics['max_amplitude']:.1f} μV")
        print(f"  基线标准差: {original_metrics['baseline_std']:.1f} μV")
        print(f"  信噪比: {original_metrics['snr']:.2f}")

        print("优化后Test3质量指标:")
        print(f"  Epochs数量: {optimized_metrics['epoch_count']}")
        print(f"  最大幅度: {optimized_metrics['max_amplitude']:.1f} μV")
        print(f"  基线标准差: {optimized_metrics['baseline_std']:.1f} μV")
        print(f"  信噪比: {optimized_metrics['snr']:.2f}")

        # 步骤6: 检测峰值
        print("\n步骤6: 检测前额叶区域峰值")
        original_peaks = detect_peaks_in_frontal_region(original_processed, times, ch_names, sampling_freq)
        optimized_peaks = detect_peaks_in_frontal_region(optimized_processed, times, ch_names, sampling_freq)

        print(f"原始Test3峰值检测: {original_peaks['count']}个峰值")
        if original_peaks['times']:
            for i, (time, amp) in enumerate(zip(original_peaks['times'], original_peaks['amplitudes'])):
                print(f"  峰值{i+1}: {time:.1f}ms, {amp:.2f}μV")

        print(f"优化后Test3峰值检测: {optimized_peaks['count']}个峰值")
        if optimized_peaks['times']:
            for i, (time, amp) in enumerate(zip(optimized_peaks['times'], optimized_peaks['amplitudes'])):
                print(f"  峰值{i+1}: {time:.1f}ms, {amp:.2f}μV")

        # 步骤7: 创建验证可视化
        print("\n步骤7: 创建验证可视化")
        fig = create_validation_visualization(
            original_processed, optimized_processed, times, ch_names,
            original_metrics, optimized_metrics,
            original_peaks, optimized_peaks
        )

        # 步骤8: 生成验证报告
        print("\n步骤8: 生成验证报告")
        save_validation_report(original_metrics, optimized_metrics, original_peaks, optimized_peaks)

        # 输出验证结论
        print("\n" + "=" * 80)
        print("Test3优化效果验证完成！")
        print("=" * 80)

        # 判断优化是否成功
        double_peak_eliminated = original_peaks['count'] >= 2 and optimized_peaks['count'] < 2
        quality_improved = (optimized_metrics['max_amplitude'] < original_metrics['max_amplitude'] * 0.5 and
                           optimized_metrics['baseline_std'] < original_metrics['baseline_std'] * 0.8)

        if double_peak_eliminated:
            print("✅ 双峰现象已成功消除")
            print(f"   峰值数量从 {original_peaks['count']} 个减少到 {optimized_peaks['count']} 个")
        else:
            print("⚠️ 双峰现象仍需进一步优化")

        if quality_improved:
            print("✅ 信号质量显著改善")
            amp_reduction = (1 - optimized_metrics['max_amplitude'] / original_metrics['max_amplitude']) * 100
            noise_reduction = (1 - optimized_metrics['baseline_std'] / original_metrics['baseline_std']) * 100
            print(f"   最大幅度降低 {amp_reduction:.1f}%")
            print(f"   基线噪声降低 {noise_reduction:.1f}%")
        else:
            print("⚠️ 信号质量改善有限")

        data_retention = optimized_metrics['epoch_count'] / original_metrics['epoch_count'] * 100
        print(f"📊 数据保留率: {data_retention:.1f}%")

        print(f"\n📁 结果保存目录: {RESULT_DIR}")
        print("   - test3_optimization_validation.png (验证图表)")
        print("   - test3_optimization_validation_report.txt (验证报告)")

        # 返回验证结果供后续使用
        return {
            'double_peak_eliminated': double_peak_eliminated,
            'quality_improved': quality_improved,
            'optimized_file': optimized_test3_file,
            'data_retention': data_retention
        }

    except Exception as e:
        print(f"❌ 验证过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def save_validation_report(original_metrics, optimized_metrics, original_peaks, optimized_peaks):
    """保存验证报告"""
    report_file = os.path.join(RESULT_DIR, 'test3_optimization_validation_report.txt')

    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("Test3数据优化效果验证报告\n")
        f.write("=" * 80 + "\n\n")

        # 1. 验证概述
        f.write("1. 验证概述\n")
        f.write("-" * 40 + "\n")
        f.write("本报告验证Test3数据优化后的效果，重点关注双峰现象是否消除以及信号质量是否改善。\n\n")

        # 2. 数据量对比
        f.write("2. 数据量对比\n")
        f.write("-" * 40 + "\n")
        f.write(f"优化前epochs数量: {original_metrics['epoch_count']}\n")
        f.write(f"优化后epochs数量: {optimized_metrics['epoch_count']}\n")
        retention_rate = optimized_metrics['epoch_count'] / original_metrics['epoch_count'] * 100
        f.write(f"数据保留率: {retention_rate:.1f}%\n\n")

        # 3. 信号质量对比
        f.write("3. 信号质量对比\n")
        f.write("-" * 40 + "\n")
        f.write("优化前:\n")
        f.write(f"  最大幅度: {original_metrics['max_amplitude']:.1f} μV\n")
        f.write(f"  平均幅度: {original_metrics['mean_amplitude']:.1f} μV\n")
        f.write(f"  基线标准差: {original_metrics['baseline_std']:.1f} μV\n")
        f.write(f"  信噪比: {original_metrics['snr']:.2f}\n\n")

        f.write("优化后:\n")
        f.write(f"  最大幅度: {optimized_metrics['max_amplitude']:.1f} μV\n")
        f.write(f"  平均幅度: {optimized_metrics['mean_amplitude']:.1f} μV\n")
        f.write(f"  基线标准差: {optimized_metrics['baseline_std']:.1f} μV\n")
        f.write(f"  信噪比: {optimized_metrics['snr']:.2f}\n\n")

        # 4. 改善程度
        f.write("4. 改善程度\n")
        f.write("-" * 40 + "\n")
        amp_reduction = (1 - optimized_metrics['max_amplitude'] / original_metrics['max_amplitude']) * 100
        noise_reduction = (1 - optimized_metrics['baseline_std'] / original_metrics['baseline_std']) * 100
        snr_change = (optimized_metrics['snr'] - original_metrics['snr']) / original_metrics['snr'] * 100

        f.write(f"最大幅度降低: {amp_reduction:.1f}%\n")
        f.write(f"基线噪声降低: {noise_reduction:.1f}%\n")
        f.write(f"信噪比变化: {snr_change:+.1f}%\n\n")

        # 5. 双峰现象检测
        f.write("5. 双峰现象检测\n")
        f.write("-" * 40 + "\n")
        f.write(f"优化前检测到 {original_peaks['count']} 个峰值:\n")
        if original_peaks['times']:
            for i, (time, amp) in enumerate(zip(original_peaks['times'], original_peaks['amplitudes'])):
                f.write(f"  峰值{i+1}: {time:.1f}ms, {amp:.2f}μV\n")
        f.write("\n")

        f.write(f"优化后检测到 {optimized_peaks['count']} 个峰值:\n")
        if optimized_peaks['times']:
            for i, (time, amp) in enumerate(zip(optimized_peaks['times'], optimized_peaks['amplitudes'])):
                f.write(f"  峰值{i+1}: {time:.1f}ms, {amp:.2f}μV\n")
        f.write("\n")

        # 6. 验证结论
        f.write("6. 验证结论\n")
        f.write("-" * 40 + "\n")

        if original_peaks['count'] >= 2 and optimized_peaks['count'] < 2:
            f.write("✅ 双峰现象已成功消除\n")
        elif original_peaks['count'] >= 2 and optimized_peaks['count'] >= 2:
            f.write("⚠️ 双峰现象仍存在，需要进一步优化\n")
        else:
            f.write("ℹ️ 原始数据无明显双峰现象\n")

        if amp_reduction > 50 and noise_reduction > 20:
            f.write("✅ 信号质量显著改善\n")
        elif amp_reduction > 20 or noise_reduction > 10:
            f.write("✅ 信号质量有所改善\n")
        else:
            f.write("⚠️ 信号质量改善有限\n")

        if retention_rate > 90:
            f.write("✅ 数据保留率良好\n")
        elif retention_rate > 70:
            f.write("✅ 数据保留率可接受\n")
        else:
            f.write("⚠️ 数据损失较多\n")

    print(f"验证报告已保存: {report_file}")

if __name__ == "__main__":
    main()
