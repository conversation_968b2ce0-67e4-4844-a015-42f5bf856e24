#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test3优化后R波位置和HEP成分验证脚本

本脚本专门验证优化后Test3数据的关键特征：
1. R波位置（0ms附近）是否为单峰模式
2. HEP成分主要出现在200-600ms窗口内
3. 基线期（-200ms到0ms）的稳定性
"""

import os
import h5py
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import font_manager
from scipy.signal import find_peaks, butter, filtfilt
from scipy.ndimage import gaussian_filter1d
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(font_path):
    font_manager.fontManager.addfont(font_path)
    plt.rcParams['font.family'] = 'LXGW Wenkai'
plt.rcParams['font.size'] = 10
plt.rcParams['axes.unicode_minus'] = False

# 定义路径
BASE_DIR = r'D:\ecgeeg\30-数据分析\5-HBA'
OPTIMIZATION_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '14_rest_vs_test_analysis', 'tp9tp10', 'test3_optimization')
RESULT_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '14_rest_vs_test_analysis', 'tp9tp10', 'test3_rwave_validation')
os.makedirs(RESULT_DIR, exist_ok=True)

# 定义关键时间窗口
R_WAVE_WINDOW = (-0.05, 0.05)    # R波窗口：-50ms到+50ms
BASELINE_WINDOW = (-0.2, 0.0)    # 基线窗口：-200ms到0ms
HEP_WINDOW = (0.2, 0.6)          # HEP窗口：200-600ms
EARLY_WINDOW = (0.0, 0.2)        # 早期窗口：0-200ms

def load_hep_data(h5_path):
    """加载HEP数据"""
    print(f"正在加载数据: {os.path.basename(h5_path)}")

    with h5py.File(h5_path, 'r') as f:
        data = f['data'][:]
        ch_names = [ch.decode('utf-8').strip() if isinstance(ch, bytes) else str(ch).strip()
                   for ch in f['ch_names'][:]]
        times = f['times'][:]

        if 'subject_ids' in f:
            subject_ids = f['subject_ids'][:]
            subject_ids = [s.decode('utf-8') if isinstance(s, bytes) else str(s)
                          for s in subject_ids]
        else:
            subject_ids = ['unknown'] * data.shape[0]

    sampling_freq = 1 / (times[1] - times[0])
    print(f"  数据形状: {data.shape}")
    print(f"  采样频率: {sampling_freq:.1f} Hz")
    print(f"  时间范围: {times[0]*1000:.1f} 到 {times[-1]*1000:.1f} ms")

    return data, ch_names, times, subject_ids, sampling_freq

def apply_standard_preprocessing(data, times, sampling_freq):
    """应用标准预处理流程"""
    print("应用标准预处理流程...")

    # 1. 带通滤波 0.5-45Hz
    print("  应用带通滤波: 0.5-45Hz")
    nyquist = sampling_freq / 2
    low_norm = max(0.5 / nyquist, 0.001)
    high_norm = min(45.0 / nyquist, 0.999)
    b, a = butter(4, [low_norm, high_norm], btype='band')

    filtered_data = np.zeros_like(data)
    for i in range(data.shape[0]):
        for j in range(data.shape[1]):
            filtered_data[i, j, :] = filtfilt(b, a, data[i, j, :])

    # 2. 基线矫正
    print("  应用基线矫正")
    baseline_mask = (times >= BASELINE_WINDOW[0]) & (times <= BASELINE_WINDOW[1])
    corrected_data = filtered_data.copy()

    for epoch in range(filtered_data.shape[0]):
        for ch in range(filtered_data.shape[1]):
            baseline_mean = np.mean(filtered_data[epoch, ch, baseline_mask])
            corrected_data[epoch, ch, :] -= baseline_mean

    # 3. 高斯平滑
    print("  应用高斯平滑，标准差: 1.5")
    smoothed_data = np.zeros_like(corrected_data)
    for i in range(corrected_data.shape[0]):
        for j in range(corrected_data.shape[1]):
            smoothed_data[i, j, :] = gaussian_filter1d(corrected_data[i, j, :], sigma=1.5)

    return smoothed_data

def analyze_rwave_and_hep_components(data, times, ch_names, sampling_freq):
    """分析R波位置和HEP成分"""
    print("\n分析R波位置和HEP成分...")

    # 选择前额叶区域
    frontal_channels = ['Fp1', 'Fp2', 'Fpz', 'AF3', 'AF4', 'AFz', 'F3', 'F4', 'Fz']
    frontal_indices = [i for i, ch in enumerate(ch_names) if ch in frontal_channels]

    if not frontal_indices:
        print("⚠️ 警告: 未找到前额叶电极")
        return None

    print(f"使用前额叶电极: {[ch_names[i] for i in frontal_indices]}")

    # 计算前额叶区域平均波形
    frontal_avg = np.mean(data[:, frontal_indices, :], axis=(0, 1)) * 1e6

    analysis_results = {}

    # 1. R波窗口分析（-50ms到+50ms）
    print("\n1. R波窗口分析（-50ms到+50ms）")
    rwave_mask = (times >= R_WAVE_WINDOW[0]) & (times <= R_WAVE_WINDOW[1])
    rwave_signal = frontal_avg[rwave_mask]
    rwave_times = times[rwave_mask] * 1000

    # 检测R波窗口内的峰值
    rwave_peaks, _ = find_peaks(np.abs(rwave_signal),
                               height=np.max(np.abs(rwave_signal)) * 0.3,
                               distance=int(0.02 * sampling_freq))  # 最小间距20ms

    analysis_results['rwave'] = {
        'peak_count': len(rwave_peaks),
        'peak_times': rwave_times[rwave_peaks].tolist() if len(rwave_peaks) > 0 else [],
        'peak_amplitudes': rwave_signal[rwave_peaks].tolist() if len(rwave_peaks) > 0 else [],
        'signal': rwave_signal.tolist(),
        'time_axis': rwave_times.tolist(),
        'max_amplitude': np.max(np.abs(rwave_signal)),
        'is_single_peak': len(rwave_peaks) <= 1
    }

    print(f"  R波窗口检测到 {len(rwave_peaks)} 个峰值")
    if len(rwave_peaks) <= 1:
        print("  ✅ R波位置为单峰模式")
    else:
        print("  ⚠️ R波位置存在多峰")
        for i, (time, amp) in enumerate(zip(rwave_times[rwave_peaks], rwave_signal[rwave_peaks])):
            print(f"    峰值{i+1}: {time:.1f}ms, {amp:.2f}μV")

    # 2. 早期窗口分析（0-200ms）
    print("\n2. 早期窗口分析（0-200ms）")
    early_mask = (times >= EARLY_WINDOW[0]) & (times <= EARLY_WINDOW[1])
    early_signal = frontal_avg[early_mask]
    early_times = times[early_mask] * 1000

    early_peaks, _ = find_peaks(np.abs(early_signal),
                               height=np.max(np.abs(early_signal)) * 0.3,
                               distance=int(0.05 * sampling_freq))  # 最小间距50ms

    analysis_results['early'] = {
        'peak_count': len(early_peaks),
        'peak_times': early_times[early_peaks].tolist() if len(early_peaks) > 0 else [],
        'peak_amplitudes': early_signal[early_peaks].tolist() if len(early_peaks) > 0 else [],
        'signal': early_signal.tolist(),
        'time_axis': early_times.tolist(),
        'max_amplitude': np.max(np.abs(early_signal))
    }

    print(f"  早期窗口检测到 {len(early_peaks)} 个峰值")

    # 3. HEP窗口分析（200-600ms）
    print("\n3. HEP窗口分析（200-600ms）")
    hep_mask = (times >= HEP_WINDOW[0]) & (times <= HEP_WINDOW[1])
    hep_signal = frontal_avg[hep_mask]
    hep_times = times[hep_mask] * 1000

    hep_peaks, _ = find_peaks(np.abs(hep_signal),
                             height=np.max(np.abs(hep_signal)) * 0.3,
                             distance=int(0.05 * sampling_freq))  # 最小间距50ms

    analysis_results['hep'] = {
        'peak_count': len(hep_peaks),
        'peak_times': hep_times[hep_peaks].tolist() if len(hep_peaks) > 0 else [],
        'peak_amplitudes': hep_signal[hep_peaks].tolist() if len(hep_peaks) > 0 else [],
        'signal': hep_signal.tolist(),
        'time_axis': hep_times.tolist(),
        'max_amplitude': np.max(np.abs(hep_signal))
    }

    print(f"  HEP窗口检测到 {len(hep_peaks)} 个峰值")
    if len(hep_peaks) > 0:
        for i, (time, amp) in enumerate(zip(hep_times[hep_peaks], hep_signal[hep_peaks])):
            print(f"    峰值{i+1}: {time:.1f}ms, {amp:.2f}μV")

    # 4. 基线稳定性分析
    print("\n4. 基线稳定性分析（-200ms到0ms）")
    baseline_mask = (times >= BASELINE_WINDOW[0]) & (times <= BASELINE_WINDOW[1])
    baseline_signal = frontal_avg[baseline_mask]
    baseline_times = times[baseline_mask] * 1000

    baseline_std = np.std(baseline_signal)
    baseline_mean = np.mean(baseline_signal)
    baseline_range = np.max(baseline_signal) - np.min(baseline_signal)

    analysis_results['baseline'] = {
        'std': baseline_std,
        'mean': baseline_mean,
        'range': baseline_range,
        'signal': baseline_signal.tolist(),
        'time_axis': baseline_times.tolist(),
        'is_stable': baseline_std < 1.0  # 1μV标准差阈值
    }

    print(f"  基线标准差: {baseline_std:.2f} μV")
    print(f"  基线均值: {baseline_mean:.2f} μV")
    print(f"  基线范围: {baseline_range:.2f} μV")
    if baseline_std < 1.0:
        print("  ✅ 基线稳定")
    else:
        print("  ⚠️ 基线不够稳定")

    # 5. 整体评估
    print("\n5. 整体评估")
    rwave_ok = analysis_results['rwave']['is_single_peak']
    baseline_ok = analysis_results['baseline']['is_stable']
    hep_present = analysis_results['hep']['peak_count'] > 0

    analysis_results['overall'] = {
        'rwave_single_peak': rwave_ok,
        'baseline_stable': baseline_ok,
        'hep_components_present': hep_present,
        'quality_score': sum([rwave_ok, baseline_ok, hep_present])
    }

    print(f"  R波单峰: {'✅' if rwave_ok else '❌'}")
    print(f"  基线稳定: {'✅' if baseline_ok else '❌'}")
    print(f"  HEP成分存在: {'✅' if hep_present else '❌'}")
    print(f"  总体质量评分: {analysis_results['overall']['quality_score']}/3")

    return analysis_results

def create_rwave_hep_visualization(data, times, ch_names, analysis_results):
    """创建R波和HEP成分可视化"""
    print("\n创建R波和HEP成分可视化...")

    # 创建2行2列的图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Test3优化后R波位置和HEP成分验证\n'
                 '双侧乳突参考 (TP9/TP10) | 0.5-45Hz + 高斯平滑',
                 fontsize=16, fontweight='bold')

    # 选择前额叶区域
    frontal_channels = ['Fp1', 'Fp2', 'Fpz', 'AF3', 'AF4', 'AFz', 'F3', 'F4', 'Fz']
    frontal_indices = [i for i, ch in enumerate(ch_names) if ch in frontal_channels]
    frontal_avg = np.mean(data[:, frontal_indices, :], axis=(0, 1)) * 1e6

    # 1. R波窗口详细分析（左上）
    ax1 = axes[0, 0]
    rwave_data = analysis_results['rwave']
    rwave_times = np.array(rwave_data['time_axis'])
    rwave_signal = np.array(rwave_data['signal'])

    ax1.plot(rwave_times, rwave_signal, 'b-', linewidth=2, label='R波窗口信号')

    # 标记峰值
    if rwave_data['peak_times']:
        for i, (time, amp) in enumerate(zip(rwave_data['peak_times'], rwave_data['peak_amplitudes'])):
            ax1.plot(time, amp, 'ro', markersize=8)
            ax1.annotate(f'{time:.1f}ms', (time, amp),
                        xytext=(5, 5), textcoords='offset points', fontsize=8)

    ax1.axvline(0, color='red', linestyle='--', alpha=0.7, label='R波位置')
    ax1.set_xlabel('时间 (ms)')
    ax1.set_ylabel('幅值 (μV)')
    ax1.set_title(f'R波窗口分析 (-50ms到+50ms)\n检测到{rwave_data["peak_count"]}个峰值')
    ax1.grid(True, alpha=0.3)
    ax1.legend()

    # 2. HEP窗口详细分析（右上）
    ax2 = axes[0, 1]
    hep_data = analysis_results['hep']
    hep_times = np.array(hep_data['time_axis'])
    hep_signal = np.array(hep_data['signal'])

    ax2.plot(hep_times, hep_signal, 'g-', linewidth=2, label='HEP窗口信号')

    # 标记峰值
    if hep_data['peak_times']:
        for i, (time, amp) in enumerate(zip(hep_data['peak_times'], hep_data['peak_amplitudes'])):
            ax2.plot(time, amp, 'ro', markersize=8)
            ax2.annotate(f'{time:.1f}ms', (time, amp),
                        xytext=(5, 5), textcoords='offset points', fontsize=8)

    ax2.set_xlabel('时间 (ms)')
    ax2.set_ylabel('幅值 (μV)')
    ax2.set_title(f'HEP窗口分析 (200-600ms)\n检测到{hep_data["peak_count"]}个峰值')
    ax2.grid(True, alpha=0.3)
    ax2.legend()

    # 3. 基线稳定性分析（左下）
    ax3 = axes[1, 0]
    baseline_data = analysis_results['baseline']
    baseline_times = np.array(baseline_data['time_axis'])
    baseline_signal = np.array(baseline_data['signal'])

    ax3.plot(baseline_times, baseline_signal, 'm-', linewidth=2, label='基线信号')
    ax3.axhline(baseline_data['mean'], color='red', linestyle='--', alpha=0.7, label=f'均值: {baseline_data["mean"]:.2f}μV')
    ax3.fill_between(baseline_times,
                     baseline_data['mean'] - baseline_data['std'],
                     baseline_data['mean'] + baseline_data['std'],
                     alpha=0.3, color='red', label=f'±1σ: {baseline_data["std"]:.2f}μV')

    ax3.set_xlabel('时间 (ms)')
    ax3.set_ylabel('幅值 (μV)')
    ax3.set_title(f'基线稳定性分析 (-200ms到0ms)\n标准差: {baseline_data["std"]:.2f}μV')
    ax3.grid(True, alpha=0.3)
    ax3.legend()

    # 4. 完整时间序列概览（右下）
    ax4 = axes[1, 1]
    ax4.plot(times * 1000, frontal_avg, 'k-', linewidth=1.5, label='完整HEP信号')

    # 标记不同时间窗口
    ax4.axvspan(-50, 50, alpha=0.2, color='blue', label='R波窗口')
    ax4.axvspan(0, 200, alpha=0.2, color='yellow', label='早期窗口')
    ax4.axvspan(200, 600, alpha=0.2, color='green', label='HEP窗口')
    ax4.axvspan(-200, 0, alpha=0.2, color='purple', label='基线窗口')

    ax4.axvline(0, color='red', linestyle='--', alpha=0.7, label='R波位置')
    ax4.set_xlim(-200, 650)
    ax4.set_xlabel('时间 (ms)')
    ax4.set_ylabel('幅值 (μV)')
    ax4.set_title('完整时间序列和时间窗口')
    ax4.grid(True, alpha=0.3)
    ax4.legend()

    plt.tight_layout()

    # 保存图片
    output_file = os.path.join(RESULT_DIR, 'test3_rwave_hep_validation.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"R波和HEP验证图表已保存: {output_file}")

    plt.show()
    return fig

def save_rwave_hep_validation_report(analysis_results):
    """保存R波和HEP验证报告"""
    report_file = os.path.join(RESULT_DIR, 'test3_rwave_hep_validation_report.txt')

    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("Test3优化后R波位置和HEP成分验证报告\n")
        f.write("=" * 80 + "\n\n")

        # 1. 验证概述
        f.write("1. 验证概述\n")
        f.write("-" * 40 + "\n")
        f.write("本报告验证优化后Test3数据的关键生理特征：\n")
        f.write("- R波位置（0ms附近）是否为单峰模式\n")
        f.write("- HEP成分主要出现在200-600ms窗口内\n")
        f.write("- 基线期（-200ms到0ms）的稳定性\n\n")

        # 2. R波窗口分析
        f.write("2. R波窗口分析（-50ms到+50ms）\n")
        f.write("-" * 40 + "\n")
        rwave_data = analysis_results['rwave']
        f.write(f"检测到峰值数量: {rwave_data['peak_count']}\n")
        f.write(f"最大幅度: {rwave_data['max_amplitude']:.2f} μV\n")
        f.write(f"单峰模式: {'是' if rwave_data['is_single_peak'] else '否'}\n")

        if rwave_data['peak_times']:
            f.write("峰值详情:\n")
            for i, (time, amp) in enumerate(zip(rwave_data['peak_times'], rwave_data['peak_amplitudes'])):
                f.write(f"  峰值{i+1}: {time:.1f}ms, {amp:.2f}μV\n")
        f.write("\n")

        # 3. 早期窗口分析
        f.write("3. 早期窗口分析（0-200ms）\n")
        f.write("-" * 40 + "\n")
        early_data = analysis_results['early']
        f.write(f"检测到峰值数量: {early_data['peak_count']}\n")
        f.write(f"最大幅度: {early_data['max_amplitude']:.2f} μV\n")

        if early_data['peak_times']:
            f.write("峰值详情:\n")
            for i, (time, amp) in enumerate(zip(early_data['peak_times'], early_data['peak_amplitudes'])):
                f.write(f"  峰值{i+1}: {time:.1f}ms, {amp:.2f}μV\n")
        f.write("\n")

        # 4. HEP窗口分析
        f.write("4. HEP窗口分析（200-600ms）\n")
        f.write("-" * 40 + "\n")
        hep_data = analysis_results['hep']
        f.write(f"检测到峰值数量: {hep_data['peak_count']}\n")
        f.write(f"最大幅度: {hep_data['max_amplitude']:.2f} μV\n")

        if hep_data['peak_times']:
            f.write("峰值详情:\n")
            for i, (time, amp) in enumerate(zip(hep_data['peak_times'], hep_data['peak_amplitudes'])):
                f.write(f"  峰值{i+1}: {time:.1f}ms, {amp:.2f}μV\n")
        f.write("\n")

        # 5. 基线稳定性分析
        f.write("5. 基线稳定性分析（-200ms到0ms）\n")
        f.write("-" * 40 + "\n")
        baseline_data = analysis_results['baseline']
        f.write(f"基线标准差: {baseline_data['std']:.2f} μV\n")
        f.write(f"基线均值: {baseline_data['mean']:.2f} μV\n")
        f.write(f"基线范围: {baseline_data['range']:.2f} μV\n")
        f.write(f"基线稳定: {'是' if baseline_data['is_stable'] else '否'}\n\n")

        # 6. 整体评估
        f.write("6. 整体评估\n")
        f.write("-" * 40 + "\n")
        overall_data = analysis_results['overall']
        f.write(f"R波单峰模式: {'✅' if overall_data['rwave_single_peak'] else '❌'}\n")
        f.write(f"基线稳定性: {'✅' if overall_data['baseline_stable'] else '❌'}\n")
        f.write(f"HEP成分存在: {'✅' if overall_data['hep_components_present'] else '❌'}\n")
        f.write(f"总体质量评分: {overall_data['quality_score']}/3\n\n")

        # 7. 验证结论
        f.write("7. 验证结论\n")
        f.write("-" * 40 + "\n")

        if overall_data['quality_score'] == 3:
            f.write("✅ 优化后Test3数据完全符合生理特征要求\n")
        elif overall_data['quality_score'] == 2:
            f.write("✅ 优化后Test3数据基本符合生理特征要求\n")
        elif overall_data['quality_score'] == 1:
            f.write("⚠️ 优化后Test3数据部分符合生理特征要求\n")
        else:
            f.write("❌ 优化后Test3数据不符合生理特征要求\n")

        f.write("\n具体建议:\n")
        if not overall_data['rwave_single_peak']:
            f.write("- R波位置仍存在多峰，建议进一步优化R波检测算法\n")
        if not overall_data['baseline_stable']:
            f.write("- 基线不够稳定，建议调整基线矫正参数\n")
        if not overall_data['hep_components_present']:
            f.write("- HEP成分不明显，建议检查数据质量和处理流程\n")

        if overall_data['quality_score'] >= 2:
            f.write("- 可以继续进行三阶段对比分析\n")

    print(f"R波和HEP验证报告已保存: {report_file}")

def main():
    """主函数 - Test3优化后R波位置和HEP成分验证"""
    print("=" * 80)
    print("Test3优化后R波位置和HEP成分验证")
    print("=" * 80)

    try:
        # 步骤1: 加载优化后Test3数据
        print("\n步骤1: 加载优化后Test3数据")
        optimized_files = [f for f in os.listdir(OPTIMIZATION_DIR) if f.startswith('test3_optimized_epochs_') and f.endswith('.h5')]
        if not optimized_files:
            print("❌ 未找到优化后Test3数据文件")
            return

        optimized_files.sort()
        optimized_test3_file = os.path.join(OPTIMIZATION_DIR, optimized_files[-1])
        data, ch_names, times, _, sampling_freq = load_hep_data(optimized_test3_file)

        # 步骤2: 应用标准预处理
        print("\n步骤2: 应用标准预处理")
        processed_data = apply_standard_preprocessing(data, times, sampling_freq)

        # 步骤3: 分析R波位置和HEP成分
        print("\n步骤3: 分析R波位置和HEP成分")
        analysis_results = analyze_rwave_and_hep_components(processed_data, times, ch_names, sampling_freq)

        if analysis_results is None:
            print("❌ 分析失败")
            return

        # 步骤4: 创建可视化
        print("\n步骤4: 创建可视化")
        fig = create_rwave_hep_visualization(processed_data, times, ch_names, analysis_results)

        # 步骤5: 保存验证报告
        print("\n步骤5: 保存验证报告")
        save_rwave_hep_validation_report(analysis_results)

        # 输出验证结论
        print("\n" + "=" * 80)
        print("Test3优化后R波位置和HEP成分验证完成！")
        print("=" * 80)

        overall = analysis_results['overall']
        print(f"🔍 验证结果:")
        print(f"   R波单峰模式: {'✅' if overall['rwave_single_peak'] else '❌'}")
        print(f"   基线稳定性: {'✅' if overall['baseline_stable'] else '❌'}")
        print(f"   HEP成分存在: {'✅' if overall['hep_components_present'] else '❌'}")
        print(f"   总体质量评分: {overall['quality_score']}/3")

        # 详细结果
        rwave_data = analysis_results['rwave']
        hep_data = analysis_results['hep']
        baseline_data = analysis_results['baseline']

        print(f"\n📊 详细结果:")
        print(f"   R波窗口峰值: {rwave_data['peak_count']}个")
        print(f"   HEP窗口峰值: {hep_data['peak_count']}个")
        print(f"   基线标准差: {baseline_data['std']:.2f}μV")

        print(f"\n📁 结果保存目录: {RESULT_DIR}")
        print("   - test3_rwave_hep_validation.png (验证图表)")
        print("   - test3_rwave_hep_validation_report.txt (验证报告)")

        # 返回验证结果供后续使用
        return {
            'validation_passed': overall['quality_score'] >= 2,
            'rwave_single_peak': overall['rwave_single_peak'],
            'baseline_stable': overall['baseline_stable'],
            'hep_present': overall['hep_components_present'],
            'quality_score': overall['quality_score']
        }

    except Exception as e:
        print(f"❌ 验证过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
