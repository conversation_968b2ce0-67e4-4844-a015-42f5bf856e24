#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test1-Test2-Test3最终整合对比分析脚本

本脚本基于Test3双峰现象诊断分析的结果，提供最终的三阶段整合对比分析：

主要功能：
1. 使用优化后的Test3数据（如果存在）或原始Test3数据
2. 统一的数据预处理流程（基于诊断结果优化）
3. 三阶段HEP波形的科学对比分析
4. 双峰现象的最终确认和解释
5. 生成完整的科学报告和可视化图表

分析标准：
- 基线稳定性验证：<0.2μV差异
- HEP成分清晰度：>200ms后显著分化
- 信号质量控制：幅值范围1-10μV
- 图表标准：2:1宽高比，高对比度配色

技术改进：
- 基于Test3诊断结果的自适应参数
- 增强的质量控制标准
- 改进的可视化布局
- 详细的统计分析报告
"""

import os
import h5py
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import font_manager
from scipy.signal import butter, filtfilt
from scipy.ndimage import gaussian_filter1d
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(font_path):
    font_manager.fontManager.addfont(font_path)
    plt.rcParams['font.family'] = 'LXGW Wenkai'
plt.rcParams['font.size'] = 10
plt.rcParams['axes.unicode_minus'] = False

# 定义路径
BASE_DIR = r'D:\ecgeeg\30-数据分析\5-HBA'
DATA_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '13_raw_epochs', 'tp9tp10')
OPTIMIZATION_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '14_rest_vs_test_analysis', 'tp9tp10', 'test3_optimization')
RESULT_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '14_rest_vs_test_analysis', 'tp9tp10', 'final_comparison')
os.makedirs(RESULT_DIR, exist_ok=True)

# 定义脑区电极分组
BRAIN_REGIONS = {
    '左前额叶区域': ['Fp1', 'AF3', 'AF7', 'F3', 'F5'],
    '右前额叶区域': ['Fp2', 'AF4', 'AF8', 'F4', 'F6'],
    '额中央区域': ['Fpz', 'AFz', 'Fz', 'FCz'],
    '中央区域': ['Cz', 'CPz', 'Pz']
}

# 基于诊断结果的优化参数
OPTIMIZED_PARAMS = {
    'baseline_window': (-0.2, 0.0),     # 标准基线窗口
    'hep_window': (0.2, 0.65),          # HEP分析窗口
    'filter_low': 0.5,                  # 滤波下限
    'filter_high': 45.0,                # 滤波上限
    'gaussian_sigma': 1.5,              # 高斯平滑参数
    'y_range': (-10, 10),               # Y轴显示范围
    'quality_threshold': {
        'max_amplitude': 10000,         # 最大幅度阈值 (μV)
        'baseline_std': 50,             # 基线标准差阈值 (μV)
        'min_snr': 1.0                  # 最小信噪比
    }
}

# 高对比度颜色方案
PHASE_COLORS = {
    'Test1': '#FF4500',    # 深橙色
    'Test2': '#4B0082',    # 深紫色  
    'Test3': '#228B22',    # 森林绿（优化后）
    'Test3_原始': '#8B4513' # 深棕色（原始）
}

def load_hep_data(h5_path):
    """加载HEP数据"""
    print(f"正在加载数据: {os.path.basename(h5_path)}")
    
    with h5py.File(h5_path, 'r') as f:
        data = f['data'][:]
        ch_names = [ch.decode('utf-8').strip() if isinstance(ch, bytes) else str(ch).strip()
                   for ch in f['ch_names'][:]]
        times = f['times'][:]
        
        if 'subject_ids' in f:
            subject_ids = f['subject_ids'][:]
            subject_ids = [s.decode('utf-8') if isinstance(s, bytes) else str(s)
                          for s in subject_ids]
        else:
            subject_ids = ['unknown'] * data.shape[0]
    
    sampling_freq = 1 / (times[1] - times[0])
    print(f"  数据形状: {data.shape}")
    print(f"  采样频率: {sampling_freq:.1f} Hz")
    print(f"  时间范围: {times[0]*1000:.1f} 到 {times[-1]*1000:.1f} ms")
    
    return data, ch_names, times, subject_ids, sampling_freq

def find_data_files():
    """查找数据文件，优先使用优化后的Test3数据"""
    print("查找数据文件...")
    
    # 查找Test1和Test2数据
    test1_files = [f for f in os.listdir(DATA_DIR) if f.startswith('test1_raw_epochs_') and f.endswith('.h5')]
    test2_files = [f for f in os.listdir(DATA_DIR) if f.startswith('test2_raw_epochs_') and f.endswith('.h5')]
    
    if not test1_files or not test2_files:
        missing = []
        if not test1_files: missing.append('test1')
        if not test2_files: missing.append('test2')
        raise FileNotFoundError(f"未找到以下数据文件: {', '.join(missing)}")
    
    test1_files.sort()
    test2_files.sort()
    test1_file = os.path.join(DATA_DIR, test1_files[-1])
    test2_file = os.path.join(DATA_DIR, test2_files[-1])
    
    # 查找Test3数据：优先使用优化后的数据
    test3_optimized_files = []
    if os.path.exists(OPTIMIZATION_DIR):
        test3_optimized_files = [f for f in os.listdir(OPTIMIZATION_DIR) if f.startswith('test3_optimized_epochs_') and f.endswith('.h5')]
    
    if test3_optimized_files:
        test3_optimized_files.sort()
        test3_file = os.path.join(OPTIMIZATION_DIR, test3_optimized_files[-1])
        test3_source = "优化后"
        print(f"✅ 找到优化后的Test3数据: {test3_optimized_files[-1]}")
    else:
        # 使用原始Test3数据
        test3_files = [f for f in os.listdir(DATA_DIR) if f.startswith('test3_raw_epochs_') and f.endswith('.h5')]
        if not test3_files:
            raise FileNotFoundError("未找到Test3数据文件")
        test3_files.sort()
        test3_file = os.path.join(DATA_DIR, test3_files[-1])
        test3_source = "原始"
        print(f"⚠️ 使用原始Test3数据: {test3_files[-1]}")
    
    print(f"使用的数据文件:")
    print(f"  Test1: {test1_files[-1]}")
    print(f"  Test2: {test2_files[-1]}")
    print(f"  Test3: {os.path.basename(test3_file)} ({test3_source})")
    
    return test1_file, test2_file, test3_file, test3_source

def apply_optimized_preprocessing(data, sampling_freq, params):
    """应用优化的预处理流程"""
    print("应用优化预处理流程...")
    
    # 1. 带通滤波
    print(f"  带通滤波: {params['filter_low']}-{params['filter_high']} Hz")
    nyquist = sampling_freq / 2
    low_norm = max(params['filter_low'] / nyquist, 0.001)
    high_norm = min(params['filter_high'] / nyquist, 0.999)
    
    b, a = butter(4, [low_norm, high_norm], btype='band')
    
    filtered_data = np.zeros_like(data)
    for i in range(data.shape[0]):
        for j in range(data.shape[1]):
            filtered_data[i, j, :] = filtfilt(b, a, data[i, j, :])
    
    return filtered_data

def apply_unified_baseline_correction(test1_data, test2_data, test3_data, times, params):
    """统一基线矫正"""
    print("执行统一基线矫正...")
    
    baseline_mask = (times >= params['baseline_window'][0]) & (times <= params['baseline_window'][1])
    print(f"  基线窗口: {params['baseline_window'][0]*1000:.0f} 到 {params['baseline_window'][1]*1000:.0f} ms")
    
    test1_corrected = test1_data.copy()
    test2_corrected = test2_data.copy()
    test3_corrected = test3_data.copy()
    
    n_epochs_test1, n_channels, _ = test1_data.shape
    n_epochs_test2 = test2_data.shape[0]
    n_epochs_test3 = test3_data.shape[0]
    
    print(f"  处理 Test1: {n_epochs_test1} epochs, Test2: {n_epochs_test2} epochs, Test3: {n_epochs_test3} epochs")
    
    # 为每个电极计算全局基线参考
    for ch in range(n_channels):
        test1_baseline_all = test1_data[:, ch, baseline_mask]
        test2_baseline_all = test2_data[:, ch, baseline_mask]
        test3_baseline_all = test3_data[:, ch, baseline_mask]
        
        all_baseline_data = np.concatenate([test1_baseline_all.flatten(),
                                          test2_baseline_all.flatten(),
                                          test3_baseline_all.flatten()])
        global_baseline_mean = np.mean(all_baseline_data)
        
        # 对每个epoch进行基线矫正
        for epoch in range(n_epochs_test1):
            epoch_baseline_mean = np.mean(test1_data[epoch, ch, baseline_mask])
            test1_corrected[epoch, ch, :] = (test1_data[epoch, ch, :] - epoch_baseline_mean) + global_baseline_mean
        
        for epoch in range(n_epochs_test2):
            epoch_baseline_mean = np.mean(test2_data[epoch, ch, baseline_mask])
            test2_corrected[epoch, ch, :] = (test2_data[epoch, ch, :] - epoch_baseline_mean) + global_baseline_mean
        
        for epoch in range(n_epochs_test3):
            epoch_baseline_mean = np.mean(test3_data[epoch, ch, baseline_mask])
            test3_corrected[epoch, ch, :] = (test3_data[epoch, ch, :] - epoch_baseline_mean) + global_baseline_mean
    
    # 验证基线矫正效果
    test1_final_baseline = np.mean(test1_corrected[:, :, baseline_mask])
    test2_final_baseline = np.mean(test2_corrected[:, :, baseline_mask])
    test3_final_baseline = np.mean(test3_corrected[:, :, baseline_mask])
    
    baseline_diff_12 = abs(test1_final_baseline - test2_final_baseline) * 1e6
    baseline_diff_13 = abs(test1_final_baseline - test3_final_baseline) * 1e6
    baseline_diff_23 = abs(test2_final_baseline - test3_final_baseline) * 1e6
    
    print(f"  基线矫正验证:")
    print(f"    Test1基线均值: {test1_final_baseline*1e6:.3f} μV")
    print(f"    Test2基线均值: {test2_final_baseline*1e6:.3f} μV")
    print(f"    Test3基线均值: {test3_final_baseline*1e6:.3f} μV")
    print(f"    基线差异 Test1-Test2: {baseline_diff_12:.3f} μV")
    print(f"    基线差异 Test1-Test3: {baseline_diff_13:.3f} μV")
    print(f"    基线差异 Test2-Test3: {baseline_diff_23:.3f} μV")
    
    max_diff = max(baseline_diff_12, baseline_diff_13, baseline_diff_23)
    if max_diff > 0.2:
        print(f"    ⚠️ 警告: 最大基线差异较大 ({max_diff:.3f} μV > 0.2 μV)")
    else:
        print(f"    ✅ 基线矫正成功，所有差异在可接受范围内")
    
    return test1_corrected, test2_corrected, test3_corrected

def apply_gaussian_smoothing(data, sigma):
    """应用高斯平滑"""
    print(f"  应用高斯平滑，标准差: {sigma}")
    
    smoothed_data = np.zeros_like(data)
    for i in range(data.shape[0]):
        for j in range(data.shape[1]):
            smoothed_data[i, j, :] = gaussian_filter1d(data[i, j, :], sigma=sigma)
    
    return smoothed_data

def calculate_quality_metrics(data, times, params, phase_name):
    """计算数据质量指标"""
    baseline_mask = (times >= params['baseline_window'][0]) & (times <= params['baseline_window'][1])
    hep_mask = (times >= params['hep_window'][0]) & (times <= params['hep_window'][1])
    
    # 基本统计
    max_amplitude = np.max(np.abs(data)) * 1e6
    mean_amplitude = np.mean(np.abs(data)) * 1e6
    std_amplitude = np.std(data) * 1e6
    
    # 基线稳定性
    baseline_data = data[:, :, baseline_mask]
    baseline_std = np.std(baseline_data) * 1e6
    baseline_mean = np.mean(baseline_data) * 1e6
    
    # HEP窗口信号
    hep_data = data[:, :, hep_mask]
    hep_std = np.std(hep_data) * 1e6
    hep_mean = np.mean(hep_data) * 1e6
    
    # 信噪比
    snr = hep_std / baseline_std if baseline_std > 0 else 0
    
    # 质量评级
    quality_score = 0
    if max_amplitude < params['quality_threshold']['max_amplitude']:
        quality_score += 1
    if baseline_std < params['quality_threshold']['baseline_std']:
        quality_score += 1
    if snr > params['quality_threshold']['min_snr']:
        quality_score += 1
    
    quality_levels = ['差', '一般', '良好', '优秀']
    quality_level = quality_levels[quality_score]
    
    print(f"  {phase_name} 质量指标:")
    print(f"    最大幅度: {max_amplitude:.1f} μV")
    print(f"    基线标准差: {baseline_std:.1f} μV")
    print(f"    信噪比: {snr:.2f}")
    print(f"    质量评级: {quality_level} ({quality_score}/3)")
    
    return {
        'max_amplitude': max_amplitude,
        'mean_amplitude': mean_amplitude,
        'std_amplitude': std_amplitude,
        'baseline_std': baseline_std,
        'baseline_mean': baseline_mean,
        'hep_std': hep_std,
        'hep_mean': hep_mean,
        'snr': snr,
        'quality_score': quality_score,
        'quality_level': quality_level
    }
